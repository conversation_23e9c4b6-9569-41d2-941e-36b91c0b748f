#include "board.h"
#include "ReturnToZero.h"
#include "stdio.h"

/*#作者：bilibili-罗叠韵
*#说明：电赛时间紧张，每个地方都是屎堆起来的的，可以说整份代码全是屎山qwq,每个地方都很乱没有来的及整理请多多指教。
*整体思路：接收图像处理上位机4个坐标点，使用atan的方法粗略的将摄像头坐标对应到屏幕上，
*并转化为步进电机旋转角度和脉冲数，只是近似地变换了坐标，并使用直线插补直接开环控制，
*对于本题精度要求已经足够，详细见motor.c。


*串口接收部分偷懒用了接收字符串ovo
*（代码还存在一些小bug没有修复）

*按键1单击：接收4个坐标并运行
*按键1双击：回到原点0,0
*按键2单击：暂停
*/

int main(void)
{
	board_init();
	vMotor_Disenable();
	vWait_Zero();//等待调0
	vMotor_Enable();
	
	Usartdata.x_step=0;
	Usartdata.y_step=0;
  for(;;)
  {
	  if(Key_Getlevel()==true)
	  {
			_4point_receive();
		  vImage_To_Actual(usart_point,5);
	  }
  }
}
