#include "ReturnToZero.h"

uint8_t rxCmd[128] = {0}; uint8_t rxCount = 0;

void vMotor_Enable(void)
{
	ZDT_X42_V2_En_Control(M0_addr,true,1);
	ZDT_X42_V2_Receive_Data(rxCmd, &rxCount);
	delay_ms(10);
	ZDT_X42_V2_En_Control(M1_addr,true,1);
	ZDT_X42_V2_Receive_Data(rxCmd, &rxCount);
	delay_ms(10);
	ZDT_X42_V2_Synchronous_motion(All_addr);
	delay_ms(10);
}

void vMotor_Disenable(void)
{
	ZDT_X42_V2_En_Control(M0_addr,false,1);
	ZDT_X42_V2_Receive_Data(rxCmd, &rxCount);
	delay_ms(10);
	ZDT_X42_V2_En_Control(M1_addr,false,1);
	ZDT_X42_V2_Receive_Data(rxCmd, &rxCount);
	delay_ms(10);
	ZDT_X42_V2_Synchronous_motion(All_addr);
	delay_ms(10);
//	Usartdata.x_step=0;
//	Usartdata.y_step=0;
	delay_ms(10);
}

void vWait_Zero(void)
{
	for(;;)
	{
		if(Key_Getlevel()==true)
		{
			ZDT_X42_V2_Reset_CurPos_To_Zero(M0_addr);
			ZDT_X42_V2_Receive_Data(rxCmd, &rxCount);
			delay_ms(10);
			ZDT_X42_V2_Reset_CurPos_To_Zero(M1_addr);
			ZDT_X42_V2_Receive_Data(rxCmd, &rxCount);
			delay_ms(10);
			//vMotor_Enable();
			break;
		}
	}
}

void vReturn_To_Zero(void)
{
	ZDT_X42_V2_Traj_Position_Control(M0_addr,0,2000,2500,800,0.0f,1,1);
	ZDT_X42_V2_Receive_Data(rxCmd, &rxCount);
	delay_ms(10);
	ZDT_X42_V2_Traj_Position_Control(M1_addr,0,2000,2500,800,0.0f,1,1);
	ZDT_X42_V2_Receive_Data(rxCmd, &rxCount);
	delay_ms(10);
	ZDT_X42_V2_Synchronous_motion(All_addr);
	delay_ms(10);
}
